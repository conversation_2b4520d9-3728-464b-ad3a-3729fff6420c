import React from 'react';
import { render, screen } from '@testing-library/react';
import App from './App';

test('renders PDF Q&A heading', () => {
  render(<App />);
  const headingElement = screen.getByText(/PDF Question & Answer/i);
  expect(headingElement).toBeInTheDocument();
});

test('renders upload section', () => {
  render(<App />);
  const uploadElement = screen.getByText(/Upload PDF Document/i);
  expect(uploadElement).toBeInTheDocument();
});

test('renders documents section', () => {
  render(<App />);
  const documentsElement = screen.getByText(/Your Documents/i);
  expect(documentsElement).toBeInTheDocument();
});
