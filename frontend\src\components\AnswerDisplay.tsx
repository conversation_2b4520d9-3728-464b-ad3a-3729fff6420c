import React from 'react';
import { Paper, Typography, Box, Divider } from '@mui/material';
import { QuestionResponse } from '../services/api';

interface AnswerDisplayProps {
  response: QuestionResponse | null;
}

const AnswerDisplay: React.FC<AnswerDisplayProps> = ({ response }) => {
  if (!response) {
    return null;
  }

  return (
    <Paper sx={{ p: 3, mb: 4 }}>
      <Typography variant="h6" gutterBottom>
        Answer
      </Typography>
      
      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle2" color="text.secondary">
          Question:
        </Typography>
        <Typography variant="body1" sx={{ mb: 2 }}>
          {response.question}
        </Typography>
      </Box>
      
      <Divider sx={{ my: 2 }} />
      
      <Box>
        <Typography variant="subtitle2" color="text.secondary">
          Answer:
        </Typography>
        <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
          {response.answer}
        </Typography>
      </Box>
    </Paper>
  );
};

export default AnswerDisplay; 