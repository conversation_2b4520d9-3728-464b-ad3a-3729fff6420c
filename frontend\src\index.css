body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

* {
  box-sizing: border-box;
}

html, body, #root {
  height: 100%;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Loading animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Drag and drop styles */
.drag-active {
  border-color: #1976d2 !important;
  background-color: rgba(25, 118, 210, 0.04) !important;
}

.drag-reject {
  border-color: #d32f2f !important;
  background-color: rgba(211, 47, 47, 0.04) !important;
}

/* Responsive design helpers */
@media (max-width: 600px) {
  .MuiContainer-root {
    padding-left: 16px !important;
    padding-right: 16px !important;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
