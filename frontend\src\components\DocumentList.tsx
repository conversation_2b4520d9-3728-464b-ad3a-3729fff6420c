import React, { useState, useEffect } from 'react';
import { 
  List, 
  ListItem, 
  ListItemText, 
  ListItemSecondaryAction, 
  IconButton, 
  Typography, 
  Box, 
  CircularProgress,
  Paper
} from '@mui/material';
import { Delete as DeleteIcon, QuestionAnswer as QuestionAnswerIcon } from '@mui/icons-material';
import { getDocuments, deleteDocument, Document } from '../services/api';

interface DocumentListProps {
  onSelectDocument: (document: Document) => void;
  refreshTrigger: number;
}

const DocumentList: React.FC<DocumentListProps> = ({ onSelectDocument, refreshTrigger }) => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDocuments = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await getDocuments();
        setDocuments(data);
      } catch (err) {
        setError('Failed to load documents');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchDocuments();
  }, [refreshTrigger]);

  const handleDelete = async (id: number) => {
    try {
      await deleteDocument(id);
      setDocuments(documents.filter(doc => doc.id !== id));
    } catch (err) {
      console.error('Failed to delete document', err);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Typography color="error" sx={{ my: 2 }}>
        {error}
      </Typography>
    );
  }

  if (documents.length === 0) {
    return (
      <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'background.default' }}>
        <Typography variant="subtitle1" color="text.secondary">
          No documents uploaded yet
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper sx={{ mb: 4 }}>
      <List>
        {documents.map((doc) => (
          <ListItem key={doc.id} divider>
            <ListItemText
              primary={doc.filename}
              secondary={`Uploaded: ${new Date(doc.upload_date).toLocaleString()}`}
            />
            <ListItemSecondaryAction>
              <IconButton 
                edge="end" 
                aria-label="ask questions" 
                onClick={() => onSelectDocument(doc)}
                sx={{ mr: 1 }}
              >
                <QuestionAnswerIcon color="primary" />
              </IconButton>
              <IconButton 
                edge="end" 
                aria-label="delete" 
                onClick={() => handleDelete(doc.id)}
              >
                <DeleteIcon color="error" />
              </IconButton>
            </ListItemSecondaryAction>
          </ListItem>
        ))}
      </List>
    </Paper>
  );
};

export default DocumentList; 