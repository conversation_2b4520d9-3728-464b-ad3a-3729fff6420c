{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"m14.5 12.59.63 2.73c.1.43-.37.77-.75.54L12 14.42l-2.39 1.44c-.38.23-.85-.11-.75-.54l.64-2.72-2.1-1.81c-.34-.29-.16-.84.28-.88l2.78-.24 1.08-2.56c.17-.41.75-.41.92 0l1.08 2.55 2.78.24c.44.04.62.59.28.88zM4.19 4.47C3.47 4.79 3 5.51 3 6.3V11c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V6.3c0-.79-.47-1.51-1.19-1.83l-7-3.11c-.52-.23-1.11-.23-1.62 0z\"\n}), 'LocalPoliceRounded');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d"], "sources": ["D:/fullstackpy/frontend/node_modules/@mui/icons-material/esm/LocalPoliceRounded.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon( /*#__PURE__*/_jsx(\"path\", {\n  d: \"m14.5 12.59.63 2.73c.1.43-.37.77-.75.54L12 14.42l-2.39 1.44c-.38.23-.85-.11-.75-.54l.64-2.72-2.1-1.81c-.34-.29-.16-.84.28-.88l2.78-.24 1.08-2.56c.17-.41.75-.41.92 0l1.08 2.55 2.78.24c.44.04.62.59.28.88zM4.19 4.47C3.47 4.79 3 5.51 3 6.3V11c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V6.3c0-.79-.47-1.51-1.19-1.83l-7-3.11c-.52-.23-1.11-.23-1.62 0z\"\n}), 'LocalPoliceRounded');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAE,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,oBAAoB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}