# PDF Q&A Application

A full-stack application that allows users to upload PDF documents and ask questions about the content using natural language processing.

## Features

- Upload PDF documents
- View and manage uploaded documents
- Ask questions about document content
- Get AI-powered answers based on document content

## Tech Stack

### Backend
- FastAPI
- Lang<PERSON>hain/LlamaIndex for NLP processing
- SQLite for database
- PyMuPDF for PDF processing

### Frontend
- React.js with TypeScript
- Material-UI for UI components

## Setup Instructions

### Prerequisites
- Python 3.8+
- Node.js 14+
- OpenAI API key

### Backend Setup

1. Navigate to the backend directory:
   ```
   cd backend
   ```

2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

3. Create a `.env` file in the backend directory with your OpenAI API key:
   ```
   OPENAI_API_KEY=your_openai_api_key_here
   ```

4. Start the FastAPI server:
   ```
   uvicorn app.main:app --reload
   ```

   The API will be available at http://localhost:8000

### Frontend Setup

1. Navigate to the frontend directory:
   ```
   cd frontend
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Start the development server:
   ```
   npm start
   ```

   The application will be available at http://localhost:3000

## API Documentation

After starting the backend server, you can access the API documentation at:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## Application Structure

### Backend

- `app/main.py`: Main FastAPI application
- `app/routers/`: API route handlers
- `app/models/`: Database models and schemas
- `app/services/`: Business logic and services

### Frontend

- `src/components/`: React components
- `src/services/`: API service functions
- `src/App.tsx`: Main application component

## Usage

1. Upload a PDF document using the upload form
2. Select a document from the list
3. Ask a question about the document content
4. View the AI-generated answer 