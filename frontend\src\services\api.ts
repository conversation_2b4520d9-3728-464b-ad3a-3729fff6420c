import axios from 'axios';

const API_URL = 'http://localhost:8000';

const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export interface Document {
  id: number;
  filename: string;
  file_path: string;
  upload_date: string;
}

export interface QuestionRequest {
  document_id: number;
  question: string;
}

export interface QuestionResponse {
  answer: string;
  document_id: number;
  question: string;
}

export const uploadDocument = async (file: File): Promise<Document> => {
  const formData = new FormData();
  formData.append('file', file);
  
  const response = await axios.post(`${API_URL}/documents/`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  
  return response.data;
};

export const getDocuments = async (): Promise<Document[]> => {
  const response = await api.get('/documents/');
  return response.data;
};

export const getDocument = async (id: number): Promise<Document> => {
  const response = await api.get(`/documents/${id}`);
  return response.data;
};

export const deleteDocument = async (id: number): Promise<void> => {
  await api.delete(`/documents/${id}`);
};

export const askQuestion = async (request: QuestionRequest): Promise<QuestionResponse> => {
  const response = await api.post('/questions/', request);
  return response.data;
};

export default api; 