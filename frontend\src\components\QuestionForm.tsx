import React, { useState } from 'react';
import { 
  TextField, 
  Button, 
  Box, 
  Typography, 
  Paper, 
  CircularProgress,
  Alert
} from '@mui/material';
import { askQuestion, Document, QuestionResponse } from '../services/api';

interface QuestionFormProps {
  document: Document | null;
  onAnswerReceived: (response: QuestionResponse) => void;
}

const QuestionForm: React.FC<QuestionFormProps> = ({ document, onAnswerReceived }) => {
  const [question, setQuestion] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!document) {
      setError('Please select a document first');
      return;
    }
    
    if (!question.trim()) {
      setError('Please enter a question');
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      const response = await askQuestion({
        document_id: document.id,
        question: question.trim()
      });
      
      onAnswerReceived(response);
      setQuestion('');
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to get answer');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  if (!document) {
    return (
      <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'background.default', mb: 4 }}>
        <Typography variant="subtitle1" color="text.secondary">
          Select a document to ask questions
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper sx={{ p: 3, mb: 4 }}>
      <Typography variant="h6" gutterBottom>
        Ask a Question
      </Typography>
      <Typography variant="subtitle2" color="text.secondary" gutterBottom>
        Document: {document.filename}
      </Typography>
      
      <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
        <TextField
          fullWidth
          label="Your Question"
          variant="outlined"
          value={question}
          onChange={(e) => setQuestion(e.target.value)}
          disabled={loading}
          multiline
          rows={2}
          sx={{ mb: 2 }}
        />
        
        <Button
          type="submit"
          variant="contained"
          color="primary"
          disabled={loading || !question.trim()}
          sx={{ mt: 1 }}
        >
          {loading ? <CircularProgress size={24} /> : 'Ask Question'}
        </Button>
        
        {error && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {error}
          </Alert>
        )}
      </Box>
    </Paper>
  );
};

export default QuestionForm; 