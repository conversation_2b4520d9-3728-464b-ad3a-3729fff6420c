import React, { useState } from 'react';
import { Container, Grid, Typography, Box, CssBaseline, ThemeProvider, createTheme } from '@mui/material';
import Header from './components/Header';
import DocumentUpload from './components/DocumentUpload';
import DocumentList from './components/DocumentList';
import QuestionForm from './components/QuestionForm';
import AnswerDisplay from './components/AnswerDisplay';
import { Document, QuestionResponse } from './services/api';

// Create a theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

function App() {
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [response, setResponse] = useState<QuestionResponse | null>(null);

  const handleDocumentUpload = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const handleSelectDocument = (document: Document) => {
    setSelectedDocument(document);
    setResponse(null);
  };

  const handleAnswerReceived = (response: QuestionResponse) => {
    setResponse(response);
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Header />
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          PDF Question & Answer
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={5}>
            <Box>
              <DocumentUpload onUploadSuccess={handleDocumentUpload} />
              <Typography variant="h6" gutterBottom>
                Your Documents
              </Typography>
              <DocumentList 
                onSelectDocument={handleSelectDocument} 
                refreshTrigger={refreshTrigger} 
              />
            </Box>
          </Grid>
          
          <Grid item xs={12} md={7}>
            <QuestionForm 
              document={selectedDocument} 
              onAnswerReceived={handleAnswerReceived} 
            />
            <AnswerDisplay response={response} />
          </Grid>
        </Grid>
      </Container>
    </ThemeProvider>
  );
}

export default App; 