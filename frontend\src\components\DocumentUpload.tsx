import React, { useState, useCallback } from "react";
import {
  Button,
  Box,
  Typography,
  CircularProgress,
  Alert,
  Paper,
} from "@mui/material";
import {
  CloudUpload as CloudUploadIcon,
  Description as DescriptionIcon,
} from "@mui/icons-material";
import { uploadDocument } from "../services/api";

interface DocumentUploadProps {
  onUploadSuccess: () => void;
}

const DocumentUpload: React.FC<DocumentUploadProps> = ({ onUploadSuccess }) => {
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [dragActive, setDragActive] = useState(false);

  const validateFile = (file: File): boolean => {
    if (file.type !== "application/pdf") {
      setError("Please upload a PDF file");
      return false;
    }

    // Check file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      setError("File size must be less than 10MB");
      return false;
    }

    return true;
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const selectedFile = event.target.files[0];

      if (validateFile(selectedFile)) {
        setFile(selectedFile);
        setError(null);
      } else {
        setFile(null);
      }
    }
  };

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDragIn = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(true);
  }, []);

  const handleDragOut = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const droppedFile = e.dataTransfer.files[0];

      if (validateFile(droppedFile)) {
        setFile(droppedFile);
        setError(null);
      } else {
        setFile(null);
      }
    }
  }, []);

  const handleUpload = async () => {
    if (!file) {
      setError("Please select a file to upload");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      await uploadDocument(file);

      setSuccess(true);
      setFile(null);
      onUploadSuccess();

      // Reset success message after 3 seconds
      setTimeout(() => {
        setSuccess(false);
      }, 3000);
    } catch (err: any) {
      setError(err.response?.data?.detail || "Failed to upload document");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ mb: 4 }}>
      <Typography variant="h6" gutterBottom>
        Upload PDF Document
      </Typography>

      <Paper
        sx={{
          p: 3,
          border: dragActive ? "2px dashed #1976d2" : "2px dashed #ccc",
          borderRadius: 2,
          backgroundColor: dragActive
            ? "rgba(25, 118, 210, 0.04)"
            : "transparent",
          cursor: "pointer",
          transition: "all 0.3s ease",
          "&:hover": {
            borderColor: "#1976d2",
            backgroundColor: "rgba(25, 118, 210, 0.02)",
          },
        }}
        onDragEnter={handleDragIn}
        onDragLeave={handleDragOut}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          accept="application/pdf"
          style={{ display: "none" }}
          id="raised-button-file"
          type="file"
          onChange={handleFileChange}
          disabled={loading}
        />

        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: 2,
          }}
        >
          {!file ? (
            <>
              <CloudUploadIcon
                sx={{ fontSize: 48, color: "text.secondary", mb: 1 }}
              />
              <Typography
                variant="body1"
                color="text.secondary"
                textAlign="center"
              >
                Drag and drop your PDF file here, or click to select
              </Typography>
              <Typography
                variant="body2"
                color="text.secondary"
                textAlign="center"
              >
                Maximum file size: 10MB
              </Typography>
            </>
          ) : (
            <>
              <DescriptionIcon
                sx={{ fontSize: 48, color: "primary.main", mb: 1 }}
              />
              <Typography
                variant="body1"
                color="text.primary"
                textAlign="center"
              >
                {file.name}
              </Typography>
              <Typography
                variant="body2"
                color="text.secondary"
                textAlign="center"
              >
                {(file.size / (1024 * 1024)).toFixed(2)} MB
              </Typography>
            </>
          )}

          <label htmlFor="raised-button-file" style={{ width: "100%" }}>
            <Button
              variant="outlined"
              component="span"
              startIcon={<CloudUploadIcon />}
              disabled={loading}
              fullWidth
            >
              {file ? "Change File" : "Select PDF File"}
            </Button>
          </label>

          {file && (
            <Button
              variant="contained"
              color="primary"
              onClick={handleUpload}
              disabled={loading}
              fullWidth
              sx={{ mt: 1 }}
            >
              {loading ? <CircularProgress size={24} /> : "Upload Document"}
            </Button>
          )}
        </Box>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mt: 2 }}>
          Document uploaded successfully!
        </Alert>
      )}
    </Box>
  );
};

export default DocumentUpload;
