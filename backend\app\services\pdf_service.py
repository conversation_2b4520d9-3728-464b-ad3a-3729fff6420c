import os
import fitz  # PyMuPDF
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.vectorstores import FAISS
from langchain.embeddings import OpenAIEmbeddings
from langchain.chains.question_answering import load_qa_chain
from langchain.llms import OpenAI
import openai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize OpenAI API key
openai.api_key = os.getenv("OPENAI_API_KEY")

class PDFService:
    def __init__(self):
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len,
        )
        self.embeddings = OpenAIEmbeddings()
        
    def extract_text_from_pdf(self, pdf_path):
        """Extract text from PDF file"""
        doc = fitz.open(pdf_path)
        text = ""
        
        for page in doc:
            text += page.get_text()
            
        return text
    
    def process_document(self, pdf_path):
        """Process PDF document and create vector store"""
        # Extract text from PDF
        text = self.extract_text_from_pdf(pdf_path)
        
        # Split text into chunks
        text_chunks = self.text_splitter.split_text(text)
        
        # Create vector store
        vector_store = FAISS.from_texts(text_chunks, self.embeddings)
        
        return vector_store
    
    def answer_question(self, vector_store, question):
        """Answer question based on document content"""
        # Create QA chain
        qa_chain = load_qa_chain(OpenAI(temperature=0), chain_type="stuff")
        
        # Search for relevant documents
        docs = vector_store.similarity_search(question, k=4)
        
        # Generate answer
        answer = qa_chain.run(input_documents=docs, question=question)
        
        return answer 