import os
import fitz  # PyMuPDF
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.vectorstores import FAISS
from langchain.embeddings import OpenAIEmbeddings
from langchain.chains.question_answering import load_qa_chain
from langchain.llms import OpenAI
import openai
from dotenv import load_dotenv
import logging

# Load environment variables
load_dotenv()

# Initialize OpenAI API key
openai.api_key = os.getenv("OPENAI_API_KEY")

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PDFService:
    def __init__(self):
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            length_function=len,
        )

        # Check if OpenAI API key is available
        if not os.getenv("OPENAI_API_KEY"):
            raise ValueError("OPENAI_API_KEY environment variable is required")

        self.embeddings = OpenAIEmbeddings()
        
    def extract_text_from_pdf(self, pdf_path):
        """Extract text from PDF file"""
        try:
            logger.info(f"Extracting text from PDF: {pdf_path}")
            doc = fitz.open(pdf_path)
            text = ""

            for page_num, page in enumerate(doc):
                page_text = page.get_text()
                if page_text.strip():  # Only add non-empty pages
                    text += f"\n--- Page {page_num + 1} ---\n{page_text}"

            doc.close()

            if not text.strip():
                raise ValueError("No text content found in PDF")

            logger.info(f"Successfully extracted {len(text)} characters from PDF")
            return text

        except Exception as e:
            logger.error(f"Error extracting text from PDF {pdf_path}: {str(e)}")
            raise
    
    def process_document(self, pdf_path):
        """Process PDF document and create vector store"""
        try:
            logger.info(f"Processing document: {pdf_path}")

            # Extract text from PDF
            text = self.extract_text_from_pdf(pdf_path)

            # Split text into chunks
            text_chunks = self.text_splitter.split_text(text)
            logger.info(f"Split text into {len(text_chunks)} chunks")

            if not text_chunks:
                raise ValueError("No text chunks created from document")

            # Create vector store
            vector_store = FAISS.from_texts(text_chunks, self.embeddings)
            logger.info("Successfully created vector store")

            return vector_store

        except Exception as e:
            logger.error(f"Error processing document {pdf_path}: {str(e)}")
            raise
    
    def answer_question(self, vector_store, question):
        """Answer question based on document content"""
        try:
            logger.info(f"Answering question: {question[:100]}...")

            # Search for relevant documents
            docs = vector_store.similarity_search(question, k=4)
            logger.info(f"Found {len(docs)} relevant document chunks")

            if not docs:
                return "I couldn't find any relevant information in the document to answer your question."

            # Create QA chain
            qa_chain = load_qa_chain(OpenAI(temperature=0), chain_type="stuff")

            # Generate answer
            answer = qa_chain.run(input_documents=docs, question=question)
            logger.info("Successfully generated answer")

            return answer

        except Exception as e:
            logger.error(f"Error answering question: {str(e)}")
            raise